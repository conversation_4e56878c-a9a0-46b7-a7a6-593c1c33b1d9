using System;
using System.Configuration;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Web;
using MediEase.DAL;
using MediEase.Models;

namespace MediEase.Utilities
{
    public static class DatabaseInitializer
    {
        private static readonly string connectionString = ConfigurationManager.ConnectionStrings["MediEaseConnection"].ConnectionString;

        public static void InitializeDatabase()
        {
            try
            {
                using (var context = new MediEaseContext())
                {
                    // This will create the database if it doesn't exist
                    context.Database.CreateIfNotExists();

                    // Check if we need to seed data
                    if (!context.Users.Any())
                    {
                        SeedDatabase(context);
                        ErrorLogger.LogInfo("Database initialized successfully with sample data", "DatabaseInitializer");
                    }
                    else
                    {
                        ErrorLogger.LogInfo("Database already exists with data", "DatabaseInitializer");
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "DatabaseInitializer.InitializeDatabase");
                // Don't throw - let the application continue even if database init fails
            }
        }

        private static void SeedDatabase(MediEaseContext context)
        {
            // Add Categories
            var categories = new[]
            {
                new Category { Name = "Pain Relief", Description = "Medications for pain management and relief", IsActive = true },
                new Category { Name = "Antibiotics", Description = "Antimicrobial medications for treating infections", IsActive = true },
                new Category { Name = "Vitamins & Supplements", Description = "Nutritional supplements and vitamins", IsActive = true },
                new Category { Name = "Cold & Flu", Description = "Medications for cold and flu symptoms", IsActive = true },
                new Category { Name = "Digestive Health", Description = "Medications for digestive and gastrointestinal issues", IsActive = true }
            };
            context.Categories.AddRange(categories);
            context.SaveChanges();

            // Add Brands
            var brands = new[]
            {
                new Brand { Name = "Pfizer", Description = "Leading pharmaceutical company", IsActive = true },
                new Brand { Name = "Johnson & Johnson", Description = "Healthcare and pharmaceutical products", IsActive = true },
                new Brand { Name = "Bayer", Description = "German pharmaceutical and life sciences company", IsActive = true },
                new Brand { Name = "GSK", Description = "GlaxoSmithKline pharmaceutical company", IsActive = true },
                new Brand { Name = "Novartis", Description = "Swiss multinational pharmaceutical company", IsActive = true }
            };
            context.Brands.AddRange(brands);
            context.SaveChanges();

            // Add Sample Medicines
            var medicines = new[]
            {
                new Medicine
                {
                    Name = "Tylenol Extra Strength",
                    GenericName = "Acetaminophen",
                    Description = "Fast-acting pain relief for headaches, muscle aches, and fever",
                    Category = "Pain Relief",
                    Brand = "Johnson & Johnson",
                    Price = 12.99m,
                    DiscountPercentage = 10,
                    StockQuantity = 150,
                    PrescriptionRequired = false,
                    DosageForm = "Tablet",
                    Strength = "500mg",
                    PackSize = 24,
                    Unit = "tablets",
                    Manufacturer = "Johnson & Johnson",
                    IsFeatured = true,
                    AverageRating = 4.5m,
                    ReviewCount = 234,
                    PurchaseCount = 1250,
                    IsActive = true
                },
                new Medicine
                {
                    Name = "Amoxicillin",
                    GenericName = "Amoxicillin",
                    Description = "Broad-spectrum antibiotic for bacterial infections",
                    Category = "Antibiotics",
                    Brand = "Pfizer",
                    Price = 25.99m,
                    StockQuantity = 75,
                    PrescriptionRequired = true,
                    DosageForm = "Capsule",
                    Strength = "500mg",
                    PackSize = 30,
                    Unit = "capsules",
                    Manufacturer = "Pfizer",
                    AverageRating = 4.7m,
                    ReviewCount = 156,
                    PurchaseCount = 450,
                    IsActive = true
                }
            };
            context.Medicines.AddRange(medicines);
            context.SaveChanges();

            // Add Initial Admin User Only
            // Additional users should be created through the registration system
            var adminUser = new User
            {
                Email = "<EMAIL>", // Change this to your actual admin email
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("ChangeThisPassword!"), // Change this password
                FirstName = "System",
                LastName = "Administrator",
                PhoneNumber = "555-0001",
                Role = "Admin",
                IsActive = true,
                IsEmailVerified = true
            };
            context.Users.Add(adminUser);
            context.SaveChanges();
        }

        private static bool DatabaseExists()
        {
            try
            {
                using (var context = new MediEaseContext())
                {
                    return context.Database.Exists();
                }
            }
            catch
            {
                return false;
            }
        }



        public static bool TestConnection()
        {
            try
            {
                using (var context = new MediEaseContext())
                {
                    // Try to connect and execute a simple query
                    var count = context.Database.SqlQuery<int>("SELECT 1").FirstOrDefault();
                    return true;
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "DatabaseInitializer.TestConnection");
                return false;
            }
        }

        public static void ResetDatabase()
        {
            try
            {
                using (var context = new MediEaseContext())
                {
                    if (context.Database.Exists())
                    {
                        context.Database.Delete();
                    }
                    context.Database.Create();
                    SeedDatabase(context);
                    ErrorLogger.LogInfo("Database reset successfully", "DatabaseInitializer");
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "DatabaseInitializer.ResetDatabase");
                throw;
            }
        }
    }
}
