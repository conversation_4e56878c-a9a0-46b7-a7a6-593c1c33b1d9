<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="Site.master.cs" Inherits="MediEase.SiteMaster" %>
<%@ Register Src="~/Controls/AIChatbot.ascx" TagPrefix="uc" TagName="AIChatbot" %>

<!DOCTYPE html>
<html lang="en">
<head runat="server">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><%: Page.Title %> - MediEase Pharmacy</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <!-- Custom CSS -->
    <link href="~/Content/Site.css" rel="stylesheet" />
    
    <asp:ContentPlaceHolder ID="HeadContent" runat="server">
    </asp:ContentPlaceHolder>
</head>

<body>
    <form id="form1" runat="server">
        <asp:ScriptManager runat="server" EnableCdn="true">
        </asp:ScriptManager>

        <!-- Navigation Header -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
            <div class="container">
                <a class="navbar-brand" href="~/Default.aspx" runat="server">
                    <i class="fas fa-pills me-2"></i>MediEase
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="~/Default.aspx" runat="server">
                                <i class="fas fa-home me-1"></i>Home
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="~/Medicines.aspx" runat="server">
                                <i class="fas fa-capsules me-1"></i>Medicines
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="~/Prescription.aspx" runat="server">
                                <i class="fas fa-prescription me-1"></i>Upload Prescription
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="~/About.aspx" runat="server">
                                <i class="fas fa-info-circle me-1"></i>About
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="~/Contact.aspx" runat="server">
                                <i class="fas fa-phone me-1"></i>Contact
                            </a>
                        </li>
                    </ul>
                    
                    <!-- Search Bar -->
                    <div class="d-flex me-3">
                        <div class="input-group">
                            <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control" placeholder="Search medicines..." />
                            <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-outline-light" Text="Search" OnClick="btnSearch_Click" />
                        </div>
                    </div>
                    
                    <!-- User Menu -->
                    <ul class="navbar-nav">
                        <asp:PlaceHolder ID="phAuthenticated" runat="server" Visible="false">
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-shopping-cart me-1"></i>Cart
                                    <span class="badge bg-danger" id="cartCount" runat="server">0</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="~/Cart.aspx" runat="server">View Cart</a></li>
                                    <li><a class="dropdown-item" href="~/Customer/Orders.aspx" runat="server">My Orders</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user me-1"></i>
                                    <asp:Literal ID="litUserName" runat="server" />
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <asp:PlaceHolder ID="phCustomerMenu" runat="server" Visible="false">
                                        <li><a class="dropdown-item" href="~/Customer/Dashboard.aspx" runat="server">
                                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                                        <li><a class="dropdown-item" href="~/Customer/Profile.aspx" runat="server">
                                            <i class="fas fa-user-edit me-2"></i>Profile</a></li>
                                        <li><a class="dropdown-item" href="~/Customer/Orders.aspx" runat="server">
                                            <i class="fas fa-shopping-bag me-2"></i>My Orders</a></li>
                                        <li><a class="dropdown-item" href="~/Customer/Prescriptions.aspx" runat="server">
                                            <i class="fas fa-prescription-bottle me-2"></i>Prescriptions</a></li>
                                        <li><a class="dropdown-item" href="~/Customer/FamilyProfiles.aspx" runat="server">
                                            <i class="fas fa-users me-2"></i>Family Profiles</a></li>
                                        <li><a class="dropdown-item" href="~/Customer/AutoRefill.aspx" runat="server">
                                            <i class="fas fa-sync-alt me-2"></i>Auto-Refill</a></li>
                                        <li><a class="dropdown-item" href="~/Customer/HealthReminders.aspx" runat="server">
                                            <i class="fas fa-bell me-2"></i>Health Reminders</a></li>
                                        <li><a class="dropdown-item" href="~/Customer/FeedbackReviews.aspx" runat="server">
                                            <i class="fas fa-star me-2"></i>Feedback & Reviews</a></li>
                                        <li><a class="dropdown-item" href="~/Customer/AccessibilitySettings.aspx" runat="server">
                                            <i class="fas fa-universal-access me-2"></i>Accessibility</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="phPharmacistMenu" runat="server" Visible="false">
                                        <li><a class="dropdown-item" href="~/Pharmacist/Dashboard.aspx" runat="server">
                                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                                        <li><a class="dropdown-item" href="~/Pharmacist/Orders.aspx" runat="server">
                                            <i class="fas fa-clipboard-list me-2"></i>Manage Orders</a></li>
                                        <li><a class="dropdown-item" href="~/Pharmacist/Inventory.aspx" runat="server">
                                            <i class="fas fa-boxes me-2"></i>Inventory</a></li>
                                        <li><a class="dropdown-item" href="~/Pharmacist/InvoiceGeneration.aspx" runat="server">
                                            <i class="fas fa-file-invoice-dollar me-2"></i>Invoice Generation</a></li>
                                        <li><a class="dropdown-item" href="~/Pharmacist/Reports.aspx" runat="server">
                                            <i class="fas fa-chart-line me-2"></i>Reports & Analytics</a></li>
                                        <li><a class="dropdown-item" href="~/Pharmacist/PriceManagement.aspx" runat="server">
                                            <i class="fas fa-tags me-2"></i>Price Management</a></li>
                                        <li><a class="dropdown-item" href="~/Shared/InternalMessaging.aspx" runat="server">
                                            <i class="fas fa-comments me-2"></i>Internal Messaging</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                    </asp:PlaceHolder>
                                    <asp:PlaceHolder ID="phAdminMenu" runat="server" Visible="false">
                                        <li><a class="dropdown-item" href="~/Admin/Dashboard.aspx" runat="server">
                                            <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard</a></li>
                                        <li><a class="dropdown-item" href="~/Admin/UserManagement.aspx" runat="server">
                                            <i class="fas fa-users-cog me-2"></i>User Management</a></li>
                                        <li><a class="dropdown-item" href="~/Admin/MedicineManagement.aspx" runat="server">
                                            <i class="fas fa-pills me-2"></i>Medicine Management</a></li>
                                        <li><a class="dropdown-item" href="~/Admin/BulkUpload.aspx" runat="server">
                                            <i class="fas fa-upload me-2"></i>Bulk Upload</a></li>
                                        <li><a class="dropdown-item" href="~/Admin/SystemConfiguration.aspx" runat="server">
                                            <i class="fas fa-cogs me-2"></i>System Configuration</a></li>
                                        <li><a class="dropdown-item" href="~/Admin/BackupRestore.aspx" runat="server">
                                            <i class="fas fa-database me-2"></i>Backup & Restore</a></li>
                                        <li><a class="dropdown-item" href="~/Admin/ChatbotManagement.aspx" runat="server">
                                            <i class="fas fa-robot me-2"></i>Chatbot Management</a></li>
                                        <li><a class="dropdown-item" href="~/Shared/InternalMessaging.aspx" runat="server">
                                            <i class="fas fa-comments me-2"></i>Internal Messaging</a></li>
                                        <li><a class="dropdown-item" href="~/Admin/Reports.aspx" runat="server">
                                            <i class="fas fa-chart-bar me-2"></i>System Reports</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                    </asp:PlaceHolder>
                                    <li>
                                        <asp:LinkButton ID="lnkLogout" runat="server" CssClass="dropdown-item" OnClick="lnkLogout_Click">
                                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                                        </asp:LinkButton>
                                    </li>
                                </ul>
                            </li>
                        </asp:PlaceHolder>
                        
                        <asp:PlaceHolder ID="phGuest" runat="server" Visible="true">
                            <li class="nav-item">
                                <a class="nav-link" href="~/Login.aspx" runat="server">
                                    <i class="fas fa-sign-in-alt me-1"></i>Login
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="~/Register.aspx" runat="server">
                                    <i class="fas fa-user-plus me-1"></i>Register
                                </a>
                            </li>
                        </asp:PlaceHolder>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <asp:ContentPlaceHolder ID="MainContent" runat="server">
            </asp:ContentPlaceHolder>
        </main>

        <!-- Footer -->
        <footer class="bg-dark text-light py-4 mt-5">
            <div class="container">
                <div class="row">
                    <div class="col-md-4">
                        <h5><i class="fas fa-pills me-2"></i>MediEase</h5>
                        <p>Your trusted pharmacy management system with AI-powered features for better healthcare.</p>
                        <div class="social-links">
                            <a href="#" class="text-light me-3"><i class="fab fa-facebook"></i></a>
                            <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="text-light me-3"><i class="fab fa-linkedin"></i></a>
                            <a href="#" class="text-light"><i class="fab fa-instagram"></i></a>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <h6>Quick Links</h6>
                        <ul class="list-unstyled">
                            <li><a href="~/Default.aspx" runat="server" class="text-light">Home</a></li>
                            <li><a href="~/Medicines.aspx" runat="server" class="text-light">Medicines</a></li>
                            <li><a href="~/About.aspx" runat="server" class="text-light">About Us</a></li>
                            <li><a href="~/Contact.aspx" runat="server" class="text-light">Contact</a></li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6>Services</h6>
                        <ul class="list-unstyled">
                            <li><a href="~/Prescription.aspx" runat="server" class="text-light">Upload Prescription</a></li>
                            <li><a href="#" class="text-light">AI Recommendations</a></li>
                            <li><a href="#" class="text-light">Home Delivery</a></li>
                            <li><a href="#" class="text-light">24/7 Support</a></li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <h6>Contact Info</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-phone me-2"></i>+****************</li>
                            <li><i class="fas fa-envelope me-2"></i><EMAIL></li>
                            <li><i class="fas fa-map-marker-alt me-2"></i>123 Pharmacy St, Health City</li>
                        </ul>
                    </div>
                </div>
                <hr class="my-4">
                <div class="row">
                    <div class="col-md-6">
                        <p>&copy; <%: DateTime.Now.Year %> MediEase. All rights reserved.</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="#" class="text-light me-3">Privacy Policy</a>
                        <a href="#" class="text-light me-3">Terms of Service</a>
                        <a href="#" class="text-light">FAQ</a>
                    </div>
                </div>
            </div>
        </footer>

        <!-- AI Chatbot -->
        <uc:AIChatbot ID="aiChatbot" runat="server" />
    </form>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="~/Scripts/Site.js"></script>
    
    <asp:ContentPlaceHolder ID="ScriptContent" runat="server">
    </asp:ContentPlaceHolder>
</body>
</html>
