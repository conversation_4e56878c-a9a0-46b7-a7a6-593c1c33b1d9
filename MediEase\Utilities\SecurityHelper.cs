using System;
using System.Security.Cryptography;
using System.Text;
using System.Web;
using System.Web.Security;
using MediEase.Models;
using MediEase.DAL;
using System.Linq;

namespace MediEase.Utilities
{
    public static class SecurityHelper
    {
        private const int SaltSize = 16;
        private const int HashSize = 20;
        private const int Iterations = 10000;

        public static string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
        }

        public static bool VerifyPassword(string password, string hashedPassword)
        {
            try
            {
                return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
            }
            catch
            {
                return false;
            }
        }

        public static string GenerateSecureToken(int length = 32)
        {
            using (var rng = new RNGCryptoServiceProvider())
            {
                var bytes = new byte[length];
                rng.GetBytes(bytes);
                return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
            }
        }

        public static string GeneratePasswordResetToken()
        {
            return GenerateSecureToken(48);
        }

        public static string GenerateEmailVerificationToken()
        {
            return GenerateSecureToken(32);
        }

        public static bool IsValidPassword(string password)
        {
            if (string.IsNullOrWhiteSpace(password) || password.Length < 8)
                return false;

            bool hasUpper = password.Any(char.IsUpper);
            bool hasLower = password.Any(char.IsLower);
            bool hasDigit = password.Any(char.IsDigit);
            bool hasSpecial = password.Any(c => !char.IsLetterOrDigit(c));

            return hasUpper && hasLower && hasDigit && hasSpecial;
        }

        public static void SetAuthenticationCookie(User user, bool rememberMe = false)
        {
            var userData = $"{user.UserId}|{user.Role}|{user.FullName}";
            var ticket = new FormsAuthenticationTicket(
                1,
                user.Email,
                DateTime.Now,
                DateTime.Now.AddMinutes(rememberMe ? 43200 : 30), // 30 days or 30 minutes
                rememberMe,
                userData,
                FormsAuthentication.FormsCookiePath
            );

            var encryptedTicket = FormsAuthentication.Encrypt(ticket);
            var cookie = new HttpCookie(FormsAuthentication.FormsCookieName, encryptedTicket)
            {
                HttpOnly = true,
                Secure = HttpContext.Current.Request.IsSecureConnection,
                SameSite = SameSiteMode.Strict
            };

            if (rememberMe)
                cookie.Expires = DateTime.Now.AddDays(30);

            HttpContext.Current.Response.Cookies.Add(cookie);
        }

        public static UserInfo GetCurrentUser(bool loadFullDetails = false)
        {
            if (!HttpContext.Current.User.Identity.IsAuthenticated)
                return null;

            var ticket = ((FormsIdentity)HttpContext.Current.User.Identity).Ticket;
            var userData = ticket.UserData.Split('|');

            if (userData.Length >= 3)
            {
                var userInfo = new UserInfo
                {
                    UserId = int.Parse(userData[0]),
                    Role = userData[1],
                    FullName = userData[2],
                    Email = ticket.Name
                };

                if (loadFullDetails)
                {
                    // Load additional details from database
                    try
                    {
                        using (var db = new DAL.MediEaseContext())
                        {
                            var user = db.Users.Find(userInfo.UserId);
                            if (user != null)
                            {
                                userInfo.FirstName = user.FirstName;
                                userInfo.LastName = user.LastName;
                                userInfo.PhoneNumber = user.PhoneNumber;
                                userInfo.Address = user.Address;
                                userInfo.City = user.City;
                                userInfo.PostalCode = user.PostalCode;
                            }
                        }
                    }
                    catch
                    {
                        // If database access fails, return basic info
                    }
                }

                return userInfo;
            }

            return null;
        }

        public static bool IsInRole(string role)
        {
            var currentUser = GetCurrentUser();
            return currentUser?.Role?.Equals(role, StringComparison.OrdinalIgnoreCase) == true;
        }

        public static bool IsAdmin()
        {
            return IsInRole("Admin");
        }

        public static bool IsPharmacist()
        {
            return IsInRole("Pharmacist") || IsAdmin();
        }

        public static bool IsCustomer()
        {
            return IsInRole("Customer");
        }

        public static void SignOut()
        {
            FormsAuthentication.SignOut();
            HttpContext.Current.Session.Clear();
            HttpContext.Current.Session.Abandon();
        }

        public static string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            return HttpUtility.HtmlEncode(input.Trim());
        }

        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        public static bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // Remove all non-digit characters
            var digits = new string(phoneNumber.Where(char.IsDigit).ToArray());
            
            // Check if it's a valid length (10-15 digits)
            return digits.Length >= 10 && digits.Length <= 15;
        }

        public static string GetClientIPAddress()
        {
            var request = HttpContext.Current.Request;
            
            // Check for forwarded IP first
            var forwarded = request.Headers["X-Forwarded-For"];
            if (!string.IsNullOrEmpty(forwarded))
            {
                var ips = forwarded.Split(',');
                return ips[0].Trim();
            }

            // Check for real IP
            var realIp = request.Headers["X-Real-IP"];
            if (!string.IsNullOrEmpty(realIp))
                return realIp;

            // Fall back to remote address
            return request.UserHostAddress;
        }

        public static string GetUserAgent()
        {
            return HttpContext.Current.Request.UserAgent ?? string.Empty;
        }

        public static bool IsAccountLocked(User user)
        {
            return user.AccountLockedUntil.HasValue && user.AccountLockedUntil.Value > DateTime.Now;
        }

        public static void LockAccount(User user, int lockoutMinutes = 30)
        {
            user.AccountLockedUntil = DateTime.Now.AddMinutes(lockoutMinutes);
            user.FailedLoginAttempts = 0; // Reset failed attempts after locking
        }

        public static void IncrementFailedLoginAttempts(User user)
        {
            user.FailedLoginAttempts++;
            
            // Lock account after 5 failed attempts
            if (user.FailedLoginAttempts >= 5)
            {
                LockAccount(user);
            }
        }

        public static void ResetFailedLoginAttempts(User user)
        {
            user.FailedLoginAttempts = 0;
            user.AccountLockedUntil = null;
            user.LastLogin = DateTime.Now;
        }

        public static bool IsPasswordResetTokenValid(User user, string token)
        {
            return !string.IsNullOrEmpty(user.PasswordResetToken) &&
                   user.PasswordResetToken == token &&
                   user.PasswordResetExpires.HasValue &&
                   user.PasswordResetExpires.Value > DateTime.Now;
        }

        public static void SetPasswordResetToken(User user)
        {
            user.PasswordResetToken = GeneratePasswordResetToken();
            user.PasswordResetExpires = DateTime.Now.AddHours(24); // Token valid for 24 hours
        }

        public static void ClearPasswordResetToken(User user)
        {
            user.PasswordResetToken = null;
            user.PasswordResetExpires = null;
        }

        public static string EncryptSensitiveData(string data)
        {
            if (string.IsNullOrEmpty(data))
                return string.Empty;

            try
            {
                var bytes = Encoding.UTF8.GetBytes(data);
                using (var aes = Aes.Create())
                {
                    aes.Key = GetEncryptionKey();
                    aes.GenerateIV();
                    
                    using (var encryptor = aes.CreateEncryptor())
                    {
                        var encrypted = encryptor.TransformFinalBlock(bytes, 0, bytes.Length);
                        var result = new byte[aes.IV.Length + encrypted.Length];
                        Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
                        Array.Copy(encrypted, 0, result, aes.IV.Length, encrypted.Length);
                        return Convert.ToBase64String(result);
                    }
                }
            }
            catch
            {
                return data; // Return original data if encryption fails
            }
        }

        public static string DecryptSensitiveData(string encryptedData)
        {
            if (string.IsNullOrEmpty(encryptedData))
                return string.Empty;

            try
            {
                var bytes = Convert.FromBase64String(encryptedData);
                using (var aes = Aes.Create())
                {
                    aes.Key = GetEncryptionKey();
                    
                    var iv = new byte[16];
                    var encrypted = new byte[bytes.Length - 16];
                    Array.Copy(bytes, 0, iv, 0, 16);
                    Array.Copy(bytes, 16, encrypted, 0, encrypted.Length);
                    
                    aes.IV = iv;
                    
                    using (var decryptor = aes.CreateDecryptor())
                    {
                        var decrypted = decryptor.TransformFinalBlock(encrypted, 0, encrypted.Length);
                        return Encoding.UTF8.GetString(decrypted);
                    }
                }
            }
            catch
            {
                return encryptedData; // Return original data if decryption fails
            }
        }

        private static byte[] GetEncryptionKey()
        {
            // In production, this should be stored securely (e.g., in Azure Key Vault)
            var keyString = System.Configuration.ConfigurationManager.AppSettings["EncryptionKey"] ?? "MediEase2024SecureKey!@#$%^&*()";
            using (var sha256 = SHA256.Create())
            {
                return sha256.ComputeHash(Encoding.UTF8.GetBytes(keyString));
            }
        }

        public static void UpdateUserSession(Models.User user)
        {
            try
            {
                if (user != null && HttpContext.Current?.Session != null)
                {
                    var userInfo = new UserInfo
                    {
                        UserId = user.UserId,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        Email = user.Email,
                        Role = user.Role,
                        FullName = user.FullName
                    };

                    HttpContext.Current.Session["CurrentUser"] = userInfo;
                }
            }
            catch (Exception ex)
            {
                ErrorLogger.LogError(ex, "Error updating user session");
            }
        }
    }

    public class UserInfo
    {
        public int UserId { get; set; }
        public string Email { get; set; }
        public string Role { get; set; }
        public string FullName { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string PhoneNumber { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string PostalCode { get; set; }
    }
}
